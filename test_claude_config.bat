@echo off
echo ========================================
echo Claude Code 配置测试脚本
echo ========================================

echo.
echo 1. 检查环境变量设置:
echo ANTHROPIC_BASE_URL=%ANTHROPIC_BASE_URL%
echo ANTHROPIC_AUTH_TOKEN=%ANTHROPIC_AUTH_TOKEN%

echo.
echo 2. 设置正确的环境变量:
set ANTHROPIC_BASE_URL=https://open.bigmodel.cn/api/anthropic
set ANTHROPIC_AUTH_TOKEN=b129202441ce465ba912734a39babdff.jPcWhihrm3cdRuyA

echo 环境变量已设置:
echo ANTHROPIC_BASE_URL=%ANTHROPIC_BASE_URL%
echo ANTHROPIC_AUTH_TOKEN=%ANTHROPIC_AUTH_TOKEN%

echo.
echo 3. 检查Claude Code版本:
claude --version

echo.
echo 4. 测试API连接:
echo 请在新的命令行窗口中运行 'claude' 命令测试连接
echo.
echo ========================================
echo 如果仍有问题，请尝试以下步骤：
echo 1. 重启命令行窗口
echo 2. 确保Claude Code已正确安装
echo 3. 检查网络连接
echo ========================================
pause
