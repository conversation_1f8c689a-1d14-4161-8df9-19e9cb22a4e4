#!/usr/bin/env python3
"""
G3P Disk Holder Resistance Welding DOE Analysis
Comprehensive analysis of welding parameters and quality metrics
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_and_clean_data(file_path):
    """Load and clean the resistance welding data"""
    print(f"Loading data from {file_path}...")
    
    # Load data
    df = pd.read_csv(file_path)
    
    # Basic info
    print(f"Dataset shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    # Clean data
    df_clean = df.copy()
    
    # Convert numeric columns
    numeric_cols = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b', 
                   'disk_gap_a', 'disk_gap_b', 'room_temperature', 'gas_fill_pressure',
                   'gas_weight', 'electrode_pressure', 'electrode_height',
                   's1_squeeze', 's1_weld_heat', 's1_percent_current', 's1_hold',
                   's1_off', 's1_impulses', 's1_cool', 's2_squeeze', 's2_weld_heat',
                   's2_percent_current', 's2_hold', 's2_off', 's2_impulses', 's2_cool']
    
    for col in numeric_cols:
        if col in df_clean.columns:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
    
    # Filter for double_s1s2 schedule type only
    df_clean = df_clean[df_clean['schedule_type'] == 'double_s1s2'].copy()
    
    # Create derived metrics
    df_clean['weld_width_avg'] = (df_clean['weld_width_a'] + df_clean['weld_width_b']) / 2
    df_clean['disk_gap_avg'] = (df_clean['disk_gap_a'] + df_clean['disk_gap_b']) / 2
    df_clean['weld_width_consistency'] = abs(df_clean['weld_width_a'] - df_clean['weld_width_b'])
    df_clean['disk_gap_consistency'] = abs(df_clean['disk_gap_a'] - df_clean['disk_gap_b'])
    
    # Create binary success indicator (failure_type = 1 means success)
    df_clean['weld_success'] = (df_clean['weld_failure_type'] == 1).astype(int)
    df_clean['leakage_pass'] = (df_clean['leakage'] == 'Pass').astype(int)
    
    print(f"After cleaning and filtering: {df_clean.shape}")
    print(f"Double S1S2 samples: {len(df_clean)}")
    
    return df_clean

def analyze_failure_modes(df):
    """Analyze failure modes and their frequencies"""
    print("\n" + "="*60)
    print("FAILURE MODE ANALYSIS")
    print("="*60)
    
    # Failure type distribution
    failure_counts = df['weld_failure_type'].value_counts().sort_index()
    failure_details = df.groupby('weld_failure_type')['failure_details'].first()
    
    print("\nFailure Type Distribution:")
    for failure_type, count in failure_counts.items():
        detail = failure_details.get(failure_type, "Unknown")
        percentage = (count / len(df)) * 100
        print(f"Type {failure_type}: {count} samples ({percentage:.1f}%) - {detail}")
    
    # Leakage performance
    leakage_counts = df['leakage'].value_counts()
    print(f"\nLeakage Performance:")
    for status, count in leakage_counts.items():
        percentage = (count / len(df)) * 100
        print(f"{status}: {count} samples ({percentage:.1f}%)")
    
    # Success rate by parameter groups
    print(f"\nOverall Success Rate: {df['weld_success'].mean()*100:.1f}%")
    print(f"Overall Leakage Pass Rate: {df['leakage_pass'].mean()*100:.1f}%")
    
    return failure_counts, leakage_counts

def analyze_parameter_significance(df):
    """Analyze which parameters are most significant for quality metrics"""
    print("\n" + "="*60)
    print("PARAMETER SIGNIFICANCE ANALYSIS")
    print("="*60)
    
    # Define parameter columns
    s1_params = ['s1_squeeze', 's1_weld_heat', 's1_percent_current', 's1_hold', 
                 's1_off', 's1_impulses', 's1_cool']
    s2_params = ['s2_squeeze', 's2_weld_heat', 's2_percent_current', 's2_hold',
                 's2_off', 's2_impulses', 's2_cool']
    
    # Quality metrics
    quality_metrics = ['post_weld_disk_holder_height', 'weld_width_avg', 
                      'disk_gap_avg', 'weld_success', 'leakage_pass']
    
    # Calculate correlations
    param_cols = s1_params + s2_params
    available_params = [col for col in param_cols if col in df.columns]
    available_metrics = [col for col in quality_metrics if col in df.columns]
    
    correlation_matrix = df[available_params + available_metrics].corr()
    
    # Extract correlations between parameters and quality metrics
    param_quality_corr = correlation_matrix.loc[available_params, available_metrics]
    
    print("\nParameter-Quality Correlations (Top 10 by absolute value):")
    corr_flat = []
    for param in available_params:
        for metric in available_metrics:
            if not pd.isna(param_quality_corr.loc[param, metric]):
                corr_flat.append({
                    'parameter': param,
                    'quality_metric': metric,
                    'correlation': param_quality_corr.loc[param, metric],
                    'abs_correlation': abs(param_quality_corr.loc[param, metric])
                })
    
    corr_df = pd.DataFrame(corr_flat).sort_values('abs_correlation', ascending=False)
    
    for i, row in corr_df.head(10).iterrows():
        print(f"{row['parameter']} -> {row['quality_metric']}: {row['correlation']:.3f}")
    
    return param_quality_corr, corr_df

def analyze_parameter_combinations(df):
    """Analyze which parameter combinations yield best results"""
    print("\n" + "="*60)
    print("OPTIMAL PARAMETER COMBINATION ANALYSIS")
    print("="*60)
    
    # Group by key parameters and analyze success rates
    key_params = ['s1_percent_current', 's1_weld_heat', 's1_hold', 
                  's2_percent_current', 's2_weld_heat', 's2_hold']
    
    available_key_params = [col for col in key_params if col in df.columns]
    
    # Success rate by parameter combinations
    success_by_params = df.groupby(available_key_params).agg({
        'weld_success': ['count', 'mean'],
        'leakage_pass': 'mean',
        'post_weld_disk_holder_height': ['mean', 'std'],
        'weld_width_avg': ['mean', 'std']
    }).round(3)
    
    # Flatten column names
    success_by_params.columns = ['_'.join(col).strip() for col in success_by_params.columns]
    
    # Filter combinations with at least 2 samples
    success_by_params = success_by_params[success_by_params['weld_success_count'] >= 2]
    
    # Sort by success rate
    success_by_params = success_by_params.sort_values('weld_success_mean', ascending=False)
    
    print(f"\nTop Parameter Combinations (min 2 samples):")
    print("Format: [s1_current, s1_heat, s1_hold, s2_current, s2_heat, s2_hold]")
    print("-" * 80)
    
    for i, (params, row) in enumerate(success_by_params.head(10).iterrows()):
        success_rate = row['weld_success_mean'] * 100
        leak_rate = row['leakage_pass_mean'] * 100
        height_mean = row['post_weld_disk_holder_height_mean']
        height_std = row['post_weld_disk_holder_height_std']
        width_mean = row['weld_width_avg_mean']
        sample_count = int(row['weld_success_count'])
        
        print(f"{i+1:2d}. {list(params)} (n={sample_count})")
        print(f"    Success: {success_rate:5.1f}%, Leak Pass: {leak_rate:5.1f}%")
        print(f"    Height: {height_mean:.2f}±{height_std:.2f}, Width: {width_mean:.2f}")
        print()
    
    return success_by_params

def main():
    """Main analysis function"""
    print("G3P Disk Holder Resistance Welding DOE Analysis")
    print("=" * 60)
    
    # Load and analyze latest data
    latest_file = "data_2025-01-18_03-46-49.csv"
    df = load_and_clean_data(latest_file)
    
    # Perform analyses
    failure_counts, leakage_counts = analyze_failure_modes(df)
    param_quality_corr, corr_df = analyze_parameter_significance(df)
    success_by_params = analyze_parameter_combinations(df)
    
    # Summary and recommendations
    print("\n" + "="*60)
    print("SUMMARY AND RECOMMENDATIONS")
    print("="*60)
    
    success_rate = df['weld_success'].mean() * 100
    leak_pass_rate = df['leakage_pass'].mean() * 100
    
    print(f"\nCurrent Performance:")
    print(f"- Overall Success Rate: {success_rate:.1f}%")
    print(f"- Leakage Pass Rate: {leak_pass_rate:.1f}%")
    print(f"- Total Samples Analyzed: {len(df)}")
    
    # Top correlations
    print(f"\nMost Influential Parameters:")
    for i, row in corr_df.head(5).iterrows():
        print(f"- {row['parameter']} affects {row['quality_metric']} (r={row['correlation']:.3f})")
    
    print(f"\nNext Steps:")
    print(f"1. Focus optimization on parameters with highest correlations")
    print(f"2. Investigate failure modes: Type 4 (height issues) and Type 8 (cracks)")
    print(f"3. Consider robust parameter combinations from top performers")
    print(f"4. Design confirmation experiments for optimal settings")

if __name__ == "__main__":
    main()
