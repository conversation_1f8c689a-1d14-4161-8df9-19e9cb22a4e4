# G3P OP60电阻焊接工艺统计分析计划

**制定日期**: 2025年8月1日  
**分析目标**: 验证关键假设并优化工艺参数  
**数据源**: NEL_G3P_Disk_Holder_Welding_Optimization_OP60.csv  
**样本规模**: 181个样本，其中140个double_s1s2样本  

---

## 1. 分析目标明确化

### 1.1 主要分析目标

#### 目标A: H1假设验证
**假设内容**: 99%电流与Type 8裂纹失效存在显著相关性  
**验证标准**: 
- 统计显著性: p < 0.05
- 效应大小: Cohen's d > 0.5 (中等效应)
- 预测准确率: >80%

#### 目标B: H2假设验证  
**假设内容**: Type 4高度失效由多因素驱动（充气压力、S2保持时间、电极压力）  
**验证标准**:
- 多元回归R² > 0.6
- 各因子p < 0.05
- 模型预测准确率: >75%

#### 目标C: 最优参数组合识别
**分析内容**: 识别最高成功率的参数组合及其统计显著性  
**验证标准**:
- 成功率差异显著性: p < 0.05
- 样本量充足性: n ≥ 5
- 置信区间: 95%CI

#### 目标D: 质量预测模型建立
**模型内容**: 基于工艺参数预测焊接质量  
**评估标准**:
- 模型准确率: >85%
- AUC > 0.8
- 交叉验证稳定性: CV-score > 0.75

---

## 2. 微任务分解

### 2.1 数据准备任务

#### 任务2.1.1: 数据加载与初步检查
- **输入**: NEL_G3P_Disk_Holder_Welding_Optimization_OP60.csv
- **输出**: 数据基本统计信息、缺失值报告
- **方法**: pandas.describe(), info(), isnull().sum()
- **验证**: 数据完整性检查，异常值识别

#### 任务2.1.2: 数据清洗与预处理
- **处理内容**:
  - 筛选double_s1s2样本 (n=140)
  - 处理缺失值 (weld_width_a/b, disk_gap_a/b)
  - 数值型变量标准化
  - 分类变量编码
- **输出**: 清洗后的分析数据集
- **验证**: 数据质量检查报告

#### 任务2.1.3: 变量定义与分组
- **成功/失效定义**: weld_failure_type == 1 为成功，其他为失效
- **失效类型分组**: Type 4 (高度失效), Type 8 (裂纹失效), 其他
- **参数分组**: 高/中/低电流，长/短保持时间等
- **输出**: 分组变量定义表

### 2.2 H1假设验证任务

#### 任务2.2.1: 描述性统计分析
- **方法**: 分组统计分析 (groupby)
- **比较内容**: 
  - Type 8失效 vs 成功样本的S1/S2电流分布
  - 焊缝宽度差异分析
  - 电流-裂纹关联性描述
- **输出**: 描述性统计表，箱线图
- **统计指标**: 均值±标准差，中位数，四分位数

#### 任务2.2.2: 相关性分析
- **方法**: Pearson相关系数，Spearman秩相关
- **分析变量**: s1_percent_current, s2_percent_current vs Type 8失效
- **输出**: 相关系数矩阵，显著性检验结果
- **验证标准**: |r| > 0.3, p < 0.05

#### 任务2.2.3: 卡方检验
- **方法**: chi2_contingency
- **分析内容**: 99%电流 vs 裂纹失效的关联性
- **输出**: 卡方统计量，p值，Cramer's V
- **验证标准**: p < 0.05, Cramer's V > 0.3

#### 任务2.2.4: 逻辑回归分析
- **方法**: LogisticRegression
- **因变量**: Type 8失效 (0/1)
- **自变量**: s1_percent_current, s2_percent_current, weld_width_a, weld_width_b
- **输出**: 回归系数，OR值，95%CI，p值
- **验证标准**: AUC > 0.7, p < 0.05

### 2.3 H2假设验证任务

#### 任务2.3.1: 多因素方差分析
- **方法**: ANOVA, Tukey HSD
- **因变量**: post_weld_disk_holder_height
- **自变量**: gas_fill_pressure, s2_hold, electrode_pressure
- **输出**: F统计量，p值，效应大小(η²)
- **验证标准**: p < 0.05, η² > 0.1

#### 任务2.3.2: 多元线性回归
- **方法**: LinearRegression, statsmodels OLS
- **因变量**: post_weld_disk_holder_height
- **自变量**: gas_fill_pressure, s2_hold, electrode_pressure, room_temperature
- **输出**: R², 调整R², 回归系数，p值，VIF
- **验证标准**: R² > 0.6, VIF < 5

#### 任务2.3.3: 决策树分析
- **方法**: DecisionTreeClassifier
- **目标**: 识别Type 4失效的关键阈值
- **输出**: 决策规则，特征重要性，准确率
- **验证标准**: 准确率 > 75%

### 2.4 最优参数组合任务

#### 任务2.4.1: 参数组合成功率分析
- **方法**: 分组统计，置信区间计算
- **分析内容**: 不同S1/S2电流和时间组合的成功率
- **输出**: 成功率排序表，95%置信区间
- **验证标准**: 样本量 ≥ 5, CI不重叠

#### 任务2.4.2: 方差分析
- **方法**: 二因素ANOVA
- **因子**: S1电流×S2电流，S1时间×S2时间
- **输出**: 主效应，交互效应，p值
- **验证标准**: p < 0.05

#### 任务2.4.3: 最优参数推荐
- **方法**: 综合分析，多目标优化
- **考虑因素**: 成功率，样本量，工艺稳定性
- **输出**: 推荐参数组合，预期成功率，置信区间

### 2.5 预测模型建立任务

#### 任务2.5.1: 特征工程
- **方法**: 特征选择，主成分分析
- **输入特征**: 所有工艺参数，环境参数
- **输出**: 最优特征子集
- **验证**: 特征重要性排序

#### 任务2.5.2: 模型训练与验证
- **方法**: RandomForest, SVM, XGBoost
- **验证方法**: 5折交叉验证
- **输出**: 模型性能比较，最优模型
- **评估指标**: 准确率，精确率，召回率，F1-score，AUC

#### 任务2.5.3: 模型解释与应用
- **方法**: SHAP值分析，特征重要性
- **输出**: 模型解释报告，应用指南
- **验证**: 模型稳定性测试

---

## 3. 数据准备计划

### 3.1 数据字段确认

#### 关键分析字段
```
质量指标:
- weld_failure_type: 失效类型 (1=成功, 4=高度失效, 8=裂纹失效)
- post_weld_disk_holder_height: 焊后高度 (mm)
- weld_width_a, weld_width_b: 焊缝宽度 (mm)
- leakage: 气密性测试结果

工艺参数:
- s1_percent_current, s2_percent_current: S1/S2电流百分比
- s1_hold, s2_hold: S1/S2保持时间 (周期)
- electrode_pressure: 电极压力 (PSI)
- gas_fill_pressure: 充气压力 (psi)

环境参数:
- room_temperature: 环境温度 (°C)
- operator: 操作员

控制参数:
- schedule_type: 焊接程序类型 (筛选double_s1s2)
```

### 3.2 数据清洗策略

#### 缺失值处理
```
weld_width_a/b缺失值:
- 检查模式: 是否与leakage测试相关
- 处理方法: 
  - 如果leakage=Failed，设为0 (焊接失败)
  - 如果leakage=Pass，使用同组均值插补
  - 记录插补标记用于敏感性分析

gas_fill_pressure缺失值:
- 检查模式: 是否为系统性缺失
- 处理方法: 使用同日期/同操作员均值插补

其他缺失值:
- 少量缺失: 删除样本
- 系统性缺失: 单独分析
```

#### 异常值处理
```
识别标准:
- 数值型: IQR方法 (Q1-1.5*IQR, Q3+1.5*IQR)
- 工艺参数: 基于工程知识的合理范围
- 质量指标: 基于规格要求的合理范围

处理策略:
- 明显错误: 删除或修正
- 极端值: 保留但标记，进行敏感性分析
- 记录所有处理过程
```

### 3.3 变量编码方案

#### 分类变量编码
```
weld_failure_type:
- 成功: 1 → 1
- Type 4失效: 4 → 0 (高度失效)
- Type 8失效: 8 → 0 (裂纹失效)
- 其他失效: 其他 → 0

operator:
- 独热编码 (One-hot encoding)
- 如果类别过多，考虑分组

schedule_type:
- 筛选条件，不参与建模
```

#### 数值变量标准化
```
标准化方法: StandardScaler (z-score)
适用变量: 所有连续型工艺参数
保留原始值: 用于结果解释
```

---

## 4. 验证标准制定

### 4.1 假设验证标准

#### H1假设验证标准
```
统计显著性:
- 卡方检验: p < 0.05
- 逻辑回归: p < 0.05, AUC > 0.7
- 相关分析: |r| > 0.3, p < 0.05

效应大小:
- Cohen's d > 0.5 (中等效应)
- Cramer's V > 0.3 (中等关联)
- OR > 2.0 或 < 0.5 (实际意义)

实际意义:
- 99%电流组裂纹率 > 95%电流组裂纹率
- 差异具有工程意义 (>5%失效率差异)
```

#### H2假设验证标准
```
模型拟合度:
- 多元回归R² > 0.6
- 调整R² > 0.5
- F检验 p < 0.05

变量显著性:
- 各预测变量 p < 0.05
- VIF < 5 (无严重多重共线性)
- 标准化回归系数 |β| > 0.2

预测准确性:
- 交叉验证R² > 0.5
- RMSE < 0.1mm (工程精度要求)
```

### 4.2 模型评估标准

#### 分类模型评估
```
性能指标:
- 准确率 (Accuracy) > 85%
- 精确率 (Precision) > 80%
- 召回率 (Recall) > 80%
- F1-score > 0.8
- AUC > 0.8

稳定性指标:
- 5折交叉验证标准差 < 0.05
- 训练集与测试集性能差异 < 10%
```

#### 回归模型评估
```
拟合指标:
- R² > 0.7
- 调整R² > 0.65
- RMSE < 工程容差的50%

诊断指标:
- 残差正态性: Shapiro-Wilk p > 0.05
- 残差独立性: Durbin-Watson 1.5-2.5
- 同方差性: Breusch-Pagan p > 0.05
```

### 4.3 结果解释标准

#### 统计显著性解释
```
p值解释:
- p < 0.001: 极显著 (***)
- p < 0.01: 高度显著 (**)
- p < 0.05: 显著 (*)
- p ≥ 0.05: 不显著 (ns)

效应大小解释:
- Cohen's d: 0.2(小), 0.5(中), 0.8(大)
- R²: 0.01(小), 0.09(中), 0.25(大)
- Cramer's V: 0.1(小), 0.3(中), 0.5(大)
```

#### 工程意义解释
```
成功率差异:
- >10%: 工程上显著
- 5-10%: 工程上重要
- <5%: 工程上微小

参数影响:
- 电流±1%对成功率的影响
- 时间±1周期对成功率的影响
- 压力±10psi对成功率的影响
```

---

## 5. 分析执行顺序

### 第一阶段: 数据准备与探索 (30分钟)
1. 数据加载与基本检查
2. 数据清洗与预处理  
3. 描述性统计分析
4. 数据可视化探索

### 第二阶段: H1假设验证 (45分钟)
1. Type 8失效描述性分析
2. 电流-裂纹相关性分析
3. 卡方检验
4. 逻辑回归建模

### 第三阶段: H2假设验证 (45分钟)
1. Type 4失效多因素分析
2. 方差分析
3. 多元回归建模
4. 决策树分析

### 第四阶段: 参数优化分析 (30分钟)
1. 参数组合成功率分析
2. 最优参数识别
3. 工艺窗口确定

### 第五阶段: 预测模型建立 (30分钟)
1. 特征工程
2. 模型训练与比较
3. 模型验证与解释

### 第六阶段: 结果整合与建议 (30分钟)
1. 假设验证结论
2. 工艺优化建议
3. 实施指导

---

## 6. 预期输出格式

### 6.1 统计分析报告结构
```
1. 执行摘要
   - 主要发现
   - 假设验证结论
   - 关键建议

2. 数据概况
   - 样本描述
   - 数据质量报告
   - 变量分布

3. H1假设验证
   - 统计检验结果
   - 效应大小分析
   - 工程意义解释

4. H2假设验证
   - 多因素分析结果
   - 模型拟合评估
   - 预测准确性

5. 参数优化
   - 最优参数组合
   - 成功率预测
   - 工艺窗口

6. 预测模型
   - 模型性能
   - 特征重要性
   - 应用指南

7. 结论与建议
   - 验证结论
   - 优化建议
   - 实施计划
```

### 6.2 关键输出表格
- H1假设验证汇总表
- H2假设多因素分析表  
- 参数组合成功率排序表
- 模型性能比较表
- 工艺优化建议表

### 6.3 关键输出图表
- 失效模式分布图
- 电流-裂纹关系图
- 多因素影响热图
- 参数优化散点图
- 模型性能ROC曲线

---

**计划制定完成，等待确认后开始执行分析**
