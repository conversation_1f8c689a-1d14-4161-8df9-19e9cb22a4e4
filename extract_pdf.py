#!/usr/bin/env python3
import sys
import pdfplumber

def extract_pdf_text(pdf_path):
    try:
        with pdfplumber.open(pdf_path) as pdf:
            text = ""
            for i, page in enumerate(pdf.pages):
                page_text = page.extract_text()
                if page_text:
                    text += f"=== 第{i+1}页 ===\n"
                    text += page_text + "\n\n"
            return text
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 extract_pdf.py <pdf_file>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    text = extract_pdf_text(pdf_path)
    if text:
        print(text[:5000])  # 显示前5000字符
        if len(text) > 5000:
            print("\n... (内容已截断)")
    else:
        print("无法提取PDF内容")
