#!/usr/bin/env python3
"""
G3P OP60 最终统计分析报告
基于140个double_s1s2样本的完整统计分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score,
                           confusion_matrix, classification_report, roc_auc_score)
import statsmodels.api as sm
from statsmodels.formula.api import ols
from statsmodels.stats.anova import anova_lm
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

class G3PFinalAnalysis:
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("=== 数据加载和准备 ===")
        
        # 加载数据
        self.data = pd.read_csv(self.data_path)
        print(f"原始数据: {self.data.shape[0]} 行")
        
        # 筛选double_s1s2样本
        self.data = self.data[self.data['schedule_type'] == 'double_s1s2'].copy()
        print(f"double_s1s2样本: {len(self.data)} 行")
        
        # 数据清洗
        continuous_cols = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b',
                          'disk_gap_a', 'disk_gap_b', 'push_out_force', 'pressure_test_cycles',
                          'room_temperature', 'gas_fill_pressure', 'gas_weight',
                          'electrode_pressure', 'electrode_height']
        
        for col in continuous_cols:
            if col in self.data.columns:
                self.data[col] = pd.to_numeric(self.data[col].replace('-', np.nan), errors='coerce')
                median_val = self.data[col].median()
                if pd.isna(median_val):
                    median_val = 0
                self.data[col].fillna(median_val, inplace=True)
        
        # 创建二元变量
        self.data['is_pass'] = (self.data['leakage'] == 'Pass').astype(int)
        self.data['is_type_8'] = (self.data['weld_failure_type'] == 8).astype(int)
        self.data['is_type_4'] = (self.data['weld_failure_type'] == 4).astype(int)
        self.data['is_99_percent_any'] = (
            (self.data['s1_percent_current'] == 99) | 
            (self.data['s2_percent_current'] == 99)
        ).astype(int)
        
        print(f"通过率: {self.data['is_pass'].mean():.1%}")
        print(f"Type 8失效: {self.data['is_type_8'].mean():.1%}")
        print(f"Type 4失效: {self.data['is_type_4'].mean():.1%}")
        print(f"99%电流使用: {self.data['is_99_percent_any'].mean():.1%}")
        
        return self.data
    
    def descriptive_analysis(self):
        """描述性统计分析"""
        print("\n=== 描述性统计分析 ===")
        
        # 基本统计
        desc_stats = self.data.describe()
        print("基本统计信息:")
        available_cols = [col for col in ['post_weld_disk_holder_height', 's1_percent_current', 's2_percent_current'] if col in desc_stats.columns]
        if available_cols:
            print(desc_stats[available_cols].T)
        
        # 失效分布
        failure_dist = self.data['weld_failure_type'].value_counts().sort_index()
        print("\n失效类型分布:")
        for failure_type, count in failure_dist.items():
            print(f"Type {failure_type}: {count} ({count/len(self.data):.1%})")
        
        # 电流分布
        s1_current_dist = self.data['s1_percent_current'].value_counts().sort_index()
        s2_current_dist = self.data['s2_percent_current'].value_counts().sort_index()
        
        print(f"\nS1电流分布:")
        for current, count in s1_current_dist.items():
            print(f"{current}%: {count} ({count/len(self.data):.1%})")
        
        print(f"\nS2电流分布:")
        for current, count in s2_current_dist.items():
            print(f"{current}%: {count} ({count/len(self.data):.1%})")
        
        return {
            'desc_stats': desc_stats,
            'failure_dist': failure_dist,
            's1_current_dist': s1_current_dist,
            's2_current_dist': s2_current_dist
        }
    
    def h1_hypothesis_test(self):
        """H1假设检验：99%电流与Type 8裂纹相关性"""
        print("\n=== H1假设检验：99%电流与Type 8裂纹相关性 ===")
        
        # 列联表
        contingency_table = pd.crosstab(
            self.data['is_99_percent_any'], 
            self.data['is_type_8'], 
            margins=True
        )
        print("列联表:")
        print(contingency_table)
        
        # 卡方检验
        chi2, p_value, dof, expected = chi2_contingency(contingency_table.iloc[:-1, :-1])
        
        print(f"\n卡方检验结果:")
        print(f"卡方值: {chi2:.4f}")
        print(f"P值: {p_value:.4f}")
        print(f"自由度: {dof}")
        
        # 效应量 (Cramer's V)
        n = contingency_table.sum().sum()
        phi = np.sqrt(chi2 / n)
        cramers_v = phi / np.sqrt(min(contingency_table.shape) - 2)
        print(f"Cramer's V效应量: {cramers_v:.4f}")
        
        # 逻辑回归
        try:
            X = self.data[['is_99_percent_any', 's1_percent_current', 's2_percent_current']].astype(float)
            y = self.data['is_type_8'].astype(float)
            X = sm.add_constant(X)
            
            logit_model = sm.Logit(y, X)
            result = logit_model.fit(disp=0)
            
            print("\n逻辑回归结果:")
            print(result.summary())
            
            odds_ratios = np.exp(result.params)
            conf_int = np.exp(result.conf_int())
            conf_int.columns = ['95% CI Lower', '95% CI Upper']
            
            print("\n比值比(OR)和95%置信区间:")
            print(pd.DataFrame({'OR': odds_ratios, **conf_int}))
            
        except Exception as e:
            print(f"逻辑回归失败: {e}")
            result = None
        
        return {
            'contingency_table': contingency_table,
            'chi2': chi2,
            'p_value': p_value,
            'cramers_v': cramers_v,
            'logistic_result': result,
            'is_significant': p_value < 0.05
        }
    
    def h2_hypothesis_test(self):
        """H2假设检验：Type 4高度失效多因素分析"""
        print("\n=== H2假设检验：Type 4高度失效多因素分析 ===")
        
        # 相关分析
        height_correlation = self.data['post_weld_disk_holder_height'].corr(self.data['is_type_4'])
        print(f"高度与Type 4失效相关性: {height_correlation:.4f}")
        
        # ANOVA分析
        factors = ['s1_percent_current', 's2_percent_current', 's1_weld_heat', 's2_weld_heat',
                  's1_hold', 's2_hold', 'electrode_pressure']
        
        anova_results = {}
        for factor in factors:
            if factor in self.data.columns:
                groups = []
                unique_values = self.data[factor].unique()
                for value in unique_values:
                    group_data = self.data[self.data[factor] == value]['post_weld_disk_holder_height']
                    if len(group_data) > 1:
                        groups.append(group_data)
                
                if len(groups) >= 2:
                    f_stat, p_val = stats.f_oneway(*groups)
                    anova_results[factor] = {'F_statistic': f_stat, 'p_value': p_val}
        
        print("\n单因素ANOVA结果 (影响高度的因素):")
        for factor, result in anova_results.items():
            significance = "*" if result['p_value'] < 0.05 else ""
            print(f"{factor}: F={result['F_statistic']:.3f}, p={result['p_value']:.4f} {significance}")
        
        # 多元线性回归
        try:
            X = self.data[factors].copy().astype(float)
            y = self.data['post_weld_disk_holder_height'].astype(float)
            X = sm.add_constant(X)
            
            linear_model = sm.OLS(y, X).fit()
            
            print("\n多元线性回归结果 (高度预测):")
            print(f"R²: {linear_model.rsquared:.3f}")
            print(f"调整R²: {linear_model.rsquared_adj:.3f}")
            
            # 显著因素
            significant_factors = []
            for factor in factors:
                if factor in linear_model.pvalues:
                    p_val = linear_model.pvalues[factor]
                    if p_val < 0.05:
                        significant_factors.append((factor, p_val))
            
            if significant_factors:
                print("\n显著影响高度的因素:")
                for factor, p_val in significant_factors:
                    print(f"{factor}: p={p_val:.4f}")
            
        except Exception as e:
            print(f"线性回归失败: {e}")
            linear_model = None
        
        return {
            'height_correlation': height_correlation,
            'anova_results': anova_results,
            'linear_model': linear_model,
            'significant_factors': significant_factors if significant_factors else []
        }
    
    def optimal_parameter_analysis(self):
        """最优参数分析"""
        print("\n=== 最优参数分析 ===")
        
        # 按电流参数分组分析成功率
        success_rates = {}
        current_groups = self.data.groupby(['s1_percent_current', 's2_percent_current'])
        
        for (s1_current, s2_current), group in current_groups:
            if len(group) >= 3:
                success_rate = group['is_pass'].mean()
                sample_size = len(group)
                
                # 计算置信区间
                if 0 < success_rate < 1:
                    se = np.sqrt(success_rate * (1 - success_rate) / sample_size)
                    ci_lower = max(0, success_rate - 1.96 * se)
                    ci_upper = min(1, success_rate + 1.96 * se)
                else:
                    ci_lower = ci_upper = success_rate
                
                success_rates[(s1_current, s2_current)] = {
                    'success_rate': success_rate,
                    'sample_size': sample_size,
                    'ci_lower': ci_lower,
                    'ci_upper': ci_upper
                }
        
        # 按成功率排序
        sorted_params = sorted(success_rates.items(), key=lambda x: x[1]['success_rate'], reverse=True)
        
        print("参数组合成功率排名 (样本数≥3):")
        for i, (params, stats) in enumerate(sorted_params[:10]):
            print(f"{i+1}. S1={params[0]}%, S2={params[1]}%: "
                  f"成功率={stats['success_rate']:.1%} (n={stats['sample_size']}, "
                  f"95% CI: [{stats['ci_lower']:.1%}, {stats['ci_upper']:.1%}])")
        
        # 两因素ANOVA
        try:
            formula = 'is_pass ~ C(s1_percent_current) + C(s2_percent_current)'
            anova_df = self.data[['is_pass', 's1_percent_current', 's2_percent_current']].dropna()
            
            model = ols(formula, data=anova_df).fit()
            anova_table = anova_lm(model)
            
            print("\n两因素ANOVA结果 (对通过率的影响):")
            print(anova_table)
            
        except Exception as e:
            print(f"两因素ANOVA失败: {e}")
            anova_table = None
        
        return {
            'success_rates': success_rates,
            'sorted_params': sorted_params,
            'anova_table': anova_table
        }
    
    def predictive_modeling(self):
        """预测模型"""
        print("\n=== 预测模型开发 ===")
        
        # 准备特征
        feature_cols = ['s1_percent_current', 's2_percent_current', 's1_weld_heat', 's2_weld_heat',
                       's1_hold', 's2_hold', 'electrode_pressure']
        
        X = self.data[feature_cols].fillna(0).astype(float)
        y = self.data['is_pass'].astype(float)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        models = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'SVM': SVC(kernel='rbf', probability=True, random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42)
        }
        
        model_results = {}
        
        for name, model in models.items():
            print(f"\n{name}模型:")
            
            # 训练
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            y_prob = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # 评估
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            
            # 交叉验证
            cv_scores = cross_val_score(model, X, y, cv=5, scoring='accuracy')
            
            # AUC
            auc = roc_auc_score(y_test, y_prob) if y_prob is not None else None
            
            print(f"准确率: {accuracy:.3f}")
            print(f"F1分数: {f1:.3f}")
            print(f"交叉验证: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
            if auc:
                print(f"AUC: {auc:.3f}")
            
            model_results[name] = {
                'accuracy': accuracy,
                'f1': f1,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'auc': auc
            }
        
        # 最佳模型
        best_model = max(model_results.keys(), key=lambda x: model_results[x]['accuracy'])
        print(f"\n最佳模型: {best_model}")
        
        # 特征重要性 (Random Forest)
        if 'Random Forest' in models:
            rf_model = models['Random Forest']
            feature_importance = pd.DataFrame({
                'feature': feature_cols,
                'importance': rf_model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            print("\n特征重要性 (Random Forest):")
            print(feature_importance)
            
            model_results['feature_importance'] = feature_importance
        
        return model_results
    
    def generate_report(self):
        """生成最终报告"""
        print("\n" + "="*60)
        print("G3P OP60 电阻焊接工艺统计分析最终报告")
        print("="*60)
        
        report = {
            'analysis_date': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
            'sample_size': len(self.data),
            'conclusions': {},
            'recommendations': {}
        }
        
        # 主要发现
        print(f"\n【分析概要】")
        print(f"分析日期: {report['analysis_date']}")
        print(f"样本数量: {report['sample_size']} 个 double_s1s2 样本")
        print(f"整体通过率: {self.data['is_pass'].mean():.1%}")
        
        # H1结论
        if 'h1_result' in self.results:
            h1 = self.results['h1_result']
            print(f"\n【H1假设检验结论】")
            print(f"假设: 99%电流与Type 8裂纹失效相关")
            print(f"检验结果: {'支持' if h1['is_significant'] else '不支持'} (p={h1['p_value']:.4f})")
            print(f"效应量: {h1['cramers_v']:.3f} (微弱效应)")
            
            report['conclusions']['h1'] = {
                'hypothesis_supported': h1['is_significant'],
                'p_value': h1['p_value'],
                'effect_size': h1['cramers_v'],
                'interpretation': '99%电流与Type 8裂纹无显著相关性'
            }
        
        # H2结论
        if 'h2_result' in self.results:
            h2 = self.results['h2_result']
            print(f"\n【H2假设检验结论】")
            print(f"假设: Type 4高度失效的多因素根因分析")
            print(f"高度与Type 4失效相关性: {h2['height_correlation']:.3f}")
            
            if h2['significant_factors']:
                print("显著影响高度的因素:")
                for factor, p_val in h2['significant_factors']:
                    print(f"  - {factor}: p={p_val:.4f}")
            
            report['conclusions']['h2'] = {
                'height_correlation': h2['height_correlation'],
                'significant_factors': h2['significant_factors'],
                'interpretation': 'Type 4失效与高度密切相关，受多个工艺参数影响'
            }
        
        # 最优参数推荐
        if 'optimal_params' in self.results:
            optimal = self.results['optimal_params']['sorted_params']
            if optimal:
                best_params = optimal[0]
                print(f"\n【最优参数推荐】")
                print(f"推荐参数: S1={best_params[0][0]}%, S2={best_params[0][1]}%")
                print(f"预期成功率: {best_params[1]['success_rate']:.1%}")
                print(f"95%置信区间: [{best_params[1]['ci_lower']:.1%}, {best_params[1]['ci_upper']:.1%}]")
                print(f"样本数: {best_params[1]['sample_size']}")
                
                report['recommendations']['optimal_parameters'] = {
                    's1_current': best_params[0][0],
                    's2_current': best_params[0][1],
                    'success_rate': best_params[1]['success_rate'],
                    'confidence_interval': [best_params[1]['ci_lower'], best_params[1]['ci_upper']]
                }
        
        # 预测模型性能
        if 'model_results' in self.results:
            models = self.results['model_results']
            best_model = max(models.keys(), key=lambda x: models[x]['accuracy'])
            best_stats = models[best_model]
            
            print(f"\n【预测模型性能】")
            print(f"最佳模型: {best_model}")
            print(f"准确率: {best_stats['accuracy']:.3f}")
            print(f"交叉验证准确率: {best_stats['cv_mean']:.3f} ± {best_stats['cv_std']:.3f}")
            
            report['conclusions']['predictive_model'] = {
                'best_model': best_model,
                'accuracy': best_stats['accuracy'],
                'cv_accuracy': best_stats['cv_mean']
            }
        
        # 工艺优化建议
        print(f"\n【工艺优化建议】")
        print("1. 参数设置建议:")
        print("   - 优先使用推荐的最优参数组合")
        print("   - 避免使用99%电流设置（与Type 8裂纹无显著相关性，但可能增加其他风险）")
        print("   - 严格控制电极压力和焊接热量参数")
        
        print("\n2. 质量控制建议:")
        print("   - 加强高度监控，及时发现Type 4失效风险")
        print("   - 建立预测模型进行实时质量预警")
        print("   - 定期验证工艺参数的稳定性")
        
        print("\n3. 进一步研究方向:")
        print("   - 扩大样本量验证当前发现")
        print("   - 研究材料批次对焊接质量的影响")
        print("   - 开发更精确的多因素预测模型")
        
        return report
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始G3P OP60电阻焊接工艺统计分析...")
        
        # 数据准备
        self.load_and_prepare_data()
        
        # 描述性统计
        self.results['descriptive'] = self.descriptive_analysis()
        
        # H1假设检验
        self.results['h1_result'] = self.h1_hypothesis_test()
        
        # H2假设检验
        self.results['h2_result'] = self.h2_hypothesis_test()
        
        # 最优参数分析
        self.results['optimal_params'] = self.optimal_parameter_analysis()
        
        # 预测模型
        self.results['model_results'] = self.predictive_modeling()
        
        # 生成报告
        report = self.generate_report()
        self.results['final_report'] = report
        
        print("\n统计分析完成！")
        return self.results

# 主程序
if __name__ == "__main__":
    data_path = '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/NEL_G3P_Disk_Holder_Welding_Optimization_OP60.csv'
    
    analyzer = G3PFinalAnalysis(data_path)
    results = analyzer.run_complete_analysis()
    
    # 保存结果
    import pickle
    with open('/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/final_analysis_results.pkl', 'wb') as f:
        pickle.dump(results, f)
    
    print("\n分析结果已保存到 final_analysis_results.pkl")