# Claude Code 配置测试脚本 (PowerShell)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Claude Code 配置测试脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`n1. 检查当前环境变量:" -ForegroundColor Yellow
Write-Host "ANTHROPIC_BASE_URL: $env:ANTHROPIC_BASE_URL"
Write-Host "ANTHROPIC_AUTH_TOKEN: $env:ANTHROPIC_AUTH_TOKEN"

Write-Host "`n2. 设置正确的环境变量:" -ForegroundColor Yellow
$env:ANTHROPIC_BASE_URL = "https://open.bigmodel.cn/api/anthropic"
$env:ANTHROPIC_AUTH_TOKEN = "b129202441ce465ba912734a39babdff.jPcWhihrm3cdRuyA"

Write-Host "环境变量已设置:" -ForegroundColor Green
Write-Host "ANTHROPIC_BASE_URL: $env:ANTHROPIC_BASE_URL"
Write-Host "ANTHROPIC_AUTH_TOKEN: $env:ANTHROPIC_AUTH_TOKEN"

Write-Host "`n3. 检查Claude Code安装:" -ForegroundColor Yellow
try {
    $version = claude --version 2>$null
    Write-Host "Claude Code版本: $version" -ForegroundColor Green
} catch {
    Write-Host "Claude Code未安装或不在PATH中" -ForegroundColor Red
}

Write-Host "`n4. 检查配置文件:" -ForegroundColor Yellow
$configPath = "$env:USERPROFILE\.claude\settings.json"
if (Test-Path $configPath) {
    Write-Host "配置文件存在: $configPath" -ForegroundColor Green
    Get-Content $configPath | Write-Host
} else {
    Write-Host "配置文件不存在: $configPath" -ForegroundColor Red
}

Write-Host "`n5. 测试API连接:" -ForegroundColor Yellow
try {
    $headers = @{
        "x-api-key" = $env:ANTHROPIC_AUTH_TOKEN
        "anthropic-version" = "2023-06-01"
        "content-type" = "application/json"
    }
    
    $body = @{
        model = "claude-3-5-sonnet-20241022"
        max_tokens = 100
        messages = @(
            @{
                role = "user"
                content = "Hello"
            }
        )
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-RestMethod -Uri "$($env:ANTHROPIC_BASE_URL)/v1/messages" -Method Post -Headers $headers -Body $body -TimeoutSec 30
    Write-Host "API连接测试成功!" -ForegroundColor Green
} catch {
    Write-Host "API连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "测试完成！现在可以尝试运行 'claude' 命令" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
