# 修复Claude Code环境变量脚本

Write-Host "正在修复Claude Code环境变量..." -ForegroundColor Yellow

# 设置正确的环境变量
$env:ANTHROPIC_BASE_URL = "https://open.bigmodel.cn/api/anthropic"
$env:ANTHROPIC_AUTH_TOKEN = "b129202441ce465ba912734a39babdff.jPcWhihrm3cdRuyA"

Write-Host "环境变量已更新:" -ForegroundColor Green
Write-Host "ANTHROPIC_BASE_URL: $env:ANTHROPIC_BASE_URL"
Write-Host "ANTHROPIC_AUTH_TOKEN: $env:ANTHROPIC_AUTH_TOKEN"

# 测试API连接
Write-Host "`n测试API连接..." -ForegroundColor Yellow

try {
    $headers = @{
        "x-api-key" = $env:ANTHROPIC_AUTH_TOKEN
        "anthropic-version" = "2023-06-01"
        "content-type" = "application/json"
    }
    
    $body = @{
        model = "claude-3-5-sonnet-20241022"
        max_tokens = 100
        messages = @(
            @{
                role = "user"
                content = "Hello"
            }
        )
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-RestMethod -Uri "$($env:ANTHROPIC_BASE_URL)/v1/messages" -Method Post -Headers $headers -Body $body -TimeoutSec 30
    Write-Host "✅ API连接测试成功!" -ForegroundColor Green
} catch {
    Write-Host "❌ API连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n现在可以尝试运行 'claude' 命令" -ForegroundColor Green 