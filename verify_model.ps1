# 验证当前使用的模型
Write-Host "========================================" -ForegroundColor Green
Write-Host "模型验证脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 设置环境变量
$env:ANTHROPIC_BASE_URL = "https://open.bigmodel.cn/api/anthropic"
$env:ANTHROPIC_AUTH_TOKEN = "b129202441ce465ba912734a39babdff.jPcWhihrm3cdRuyA"

Write-Host "`n当前配置:" -ForegroundColor Yellow
Write-Host "API端点: $env:ANTHROPIC_BASE_URL"
Write-Host "这是智谱AI的Claude兼容API端点" -ForegroundColor Cyan

Write-Host "`n测试模型身份..." -ForegroundColor Yellow

try {
    $headers = @{
        "x-api-key" = $env:ANTHROPIC_AUTH_TOKEN
        "anthropic-version" = "2023-06-01"
        "content-type" = "application/json"
    }
    
    $body = @{
        model = "claude-3-5-sonnet-20241022"
        max_tokens = 200
        messages = @(
            @{
                role = "user"
                content = "请告诉我你是什么模型？你是Claude还是GLM？请用中文回答。"
            }
        )
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-RestMethod -Uri "$($env:ANTHROPIC_BASE_URL)/v1/messages" -Method Post -Headers $headers -Body $body -TimeoutSec 30
    
    Write-Host "`n✅ 模型响应:" -ForegroundColor Green
    Write-Host $response.content[0].text -ForegroundColor White
    
} catch {
    Write-Host "`n❌ 测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "结论:" -ForegroundColor Yellow
Write-Host "• 您使用的是智谱AI的GLM-4.5模型" -ForegroundColor Cyan
Write-Host "• 通过Claude API兼容层提供服务" -ForegroundColor Cyan
Write-Host "• 可以使用Claude Code工具，但底层是GLM-4.5" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Green
