#!/usr/bin/env python3
"""
G3P Disk Holder Resistance Welding - Detailed Failure Analysis
Focus on understanding failure modes and their relationship to parameters
"""

import csv
import statistics
from collections import defaultdict, Counter

def load_csv_data(filename):
    """Load CSV data and return as list of dictionaries"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Convert numeric fields
            numeric_fields = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b',
                            'disk_gap_a', 'disk_gap_b', 'weld_failure_type', 'room_temperature',
                            'gas_fill_pressure', 'gas_weight', 'electrode_pressure', 'electrode_height',
                            's1_squeeze', 's1_weld_heat', 's1_percent_current', 's1_hold',
                            's1_off', 's1_impulses', 's1_cool', 's2_squeeze', 's2_weld_heat',
                            's2_percent_current', 's2_hold', 's2_off', 's2_impulses', 's2_cool']
            
            for field in numeric_fields:
                if field in row and row[field] and row[field] != '-':
                    try:
                        row[field] = float(row[field])
                    except ValueError:
                        row[field] = None
                else:
                    row[field] = None
            
            # Only include double_s1s2 schedule type
            if row.get('schedule_type') == 'double_s1s2':
                data.append(row)
    
    return data

def analyze_parameter_by_failure_type(data):
    """Analyze parameter differences between failure types"""
    print("\n" + "="*70)
    print("PARAMETER ANALYSIS BY FAILURE TYPE")
    print("="*70)
    
    # Group data by failure type
    failure_groups = defaultdict(list)
    for row in data:
        failure_type = row.get('weld_failure_type')
        if failure_type is not None:
            failure_groups[int(failure_type)].append(row)
    
    # Key parameters to analyze
    key_params = ['s1_percent_current', 's1_hold', 's2_percent_current', 's2_hold']
    
    print("\nParameter Comparison Across Failure Types:")
    print("-" * 70)
    
    for param in key_params:
        print(f"\n{param.upper()}:")
        print("Failure Type | Count | Mean  | Std   | Min   | Max   | Range")
        print("-" * 60)
        
        for failure_type in sorted(failure_groups.keys()):
            values = [row[param] for row in failure_groups[failure_type] 
                     if row.get(param) is not None]
            
            if values:
                count = len(values)
                mean_val = statistics.mean(values)
                std_val = statistics.stdev(values) if len(values) > 1 else 0
                min_val = min(values)
                max_val = max(values)
                range_val = max_val - min_val
                
                print(f"Type {failure_type:2d}      | {count:5d} | {mean_val:5.1f} | {std_val:5.1f} | {min_val:5.1f} | {max_val:5.1f} | {range_val:5.1f}")

def analyze_height_failure_patterns(data):
    """Detailed analysis of height-related failures (Type 4)"""
    print("\n" + "="*70)
    print("HEIGHT FAILURE ANALYSIS (Type 4)")
    print("="*70)
    
    # Filter Type 4 failures
    height_failures = [row for row in data if row.get('weld_failure_type') == 4]
    successful_welds = [row for row in data if row.get('weld_failure_type') == 1]
    
    print(f"\nHeight Failures: {len(height_failures)} samples")
    print(f"Successful Welds: {len(successful_welds)} samples")
    
    if not height_failures:
        print("No height failures found!")
        return
    
    # Height distribution analysis
    height_fail_heights = [row['post_weld_disk_holder_height'] for row in height_failures 
                          if row.get('post_weld_disk_holder_height') is not None]
    success_heights = [row['post_weld_disk_holder_height'] for row in successful_welds 
                      if row.get('post_weld_disk_holder_height') is not None]
    
    if height_fail_heights and success_heights:
        print(f"\nHeight Statistics:")
        print(f"Failed welds:     {statistics.mean(height_fail_heights):.3f} ± {statistics.stdev(height_fail_heights):.3f} mm")
        print(f"Successful welds: {statistics.mean(success_heights):.3f} ± {statistics.stdev(success_heights):.3f} mm")
        print(f"Difference:       {statistics.mean(height_fail_heights) - statistics.mean(success_heights):+.3f} mm")
    
    # Parameter analysis for height failures
    print(f"\nParameter Analysis for Height Failures:")
    key_params = ['s1_percent_current', 's1_hold', 's2_percent_current', 's2_hold']
    
    for param in key_params:
        fail_values = [row[param] for row in height_failures if row.get(param) is not None]
        success_values = [row[param] for row in successful_welds if row.get(param) is not None]
        
        if fail_values and success_values:
            fail_mean = statistics.mean(fail_values)
            success_mean = statistics.mean(success_values)
            print(f"{param:20s}: Failed={fail_mean:5.1f}, Success={success_mean:5.1f}, Diff={fail_mean-success_mean:+5.1f}")

def analyze_crack_failure_patterns(data):
    """Detailed analysis of crack-related failures (Type 8)"""
    print("\n" + "="*70)
    print("CRACK FAILURE ANALYSIS (Type 8)")
    print("="*70)
    
    # Filter Type 8 failures
    crack_failures = [row for row in data if row.get('weld_failure_type') == 8]
    successful_welds = [row for row in data if row.get('weld_failure_type') == 1]
    
    print(f"\nCrack Failures: {len(crack_failures)} samples")
    print(f"Successful Welds: {len(successful_welds)} samples")
    
    if not crack_failures:
        print("No crack failures found!")
        return
    
    # Parameter analysis for crack failures
    print(f"\nParameter Analysis for Crack Failures:")
    key_params = ['s1_percent_current', 's1_hold', 's2_percent_current', 's2_hold']
    
    for param in key_params:
        fail_values = [row[param] for row in crack_failures if row.get(param) is not None]
        success_values = [row[param] for row in successful_welds if row.get(param) is not None]
        
        if fail_values and success_values:
            fail_mean = statistics.mean(fail_values)
            success_mean = statistics.mean(success_values)
            print(f"{param:20s}: Failed={fail_mean:5.1f}, Success={success_mean:5.1f}, Diff={fail_mean-success_mean:+5.1f}")
    
    # Weld width analysis for crack failures
    print(f"\nWeld Width Analysis for Crack Failures:")
    crack_widths_a = [row['weld_width_a'] for row in crack_failures if row.get('weld_width_a') is not None]
    crack_widths_b = [row['weld_width_b'] for row in crack_failures if row.get('weld_width_b') is not None]
    success_widths_a = [row['weld_width_a'] for row in successful_welds if row.get('weld_width_a') is not None]
    success_widths_b = [row['weld_width_b'] for row in successful_welds if row.get('weld_width_b') is not None]
    
    if crack_widths_a and success_widths_a:
        print(f"Width A - Failed: {statistics.mean(crack_widths_a):.3f}, Success: {statistics.mean(success_widths_a):.3f}")
    if crack_widths_b and success_widths_b:
        print(f"Width B - Failed: {statistics.mean(crack_widths_b):.3f}, Success: {statistics.mean(success_widths_b):.3f}")

def identify_robust_parameter_windows(data):
    """Identify parameter windows with high success rates"""
    print("\n" + "="*70)
    print("ROBUST PARAMETER WINDOW IDENTIFICATION")
    print("="*70)
    
    # Analyze success rates by parameter ranges
    successful_welds = [row for row in data if row.get('weld_failure_type') == 1]
    
    # S1 Current analysis
    print(f"\nS1 Percent Current Analysis:")
    current_ranges = [(96, 97), (97, 98), (98, 99), (99, 100)]
    
    for min_curr, max_curr in current_ranges:
        range_data = [row for row in data 
                     if row.get('s1_percent_current') is not None and 
                        min_curr <= row['s1_percent_current'] < max_curr]
        
        if range_data:
            success_count = sum(1 for row in range_data if row.get('weld_failure_type') == 1)
            success_rate = (success_count / len(range_data)) * 100
            print(f"  {min_curr}-{max_curr-1}%: {success_count}/{len(range_data)} ({success_rate:.1f}% success)")
    
    # S1 Hold time analysis
    print(f"\nS1 Hold Time Analysis:")
    hold_values = sorted(set(row['s1_hold'] for row in data if row.get('s1_hold') is not None))
    
    for hold_time in hold_values:
        range_data = [row for row in data if row.get('s1_hold') == hold_time]
        
        if range_data:
            success_count = sum(1 for row in range_data if row.get('weld_failure_type') == 1)
            success_rate = (success_count / len(range_data)) * 100
            print(f"  {hold_time} cycles: {success_count}/{len(range_data)} ({success_rate:.1f}% success)")

def main():
    """Main analysis function"""
    print("G3P Disk Holder Resistance Welding - Detailed Failure Analysis")
    print("=" * 70)
    
    # Load data from both files
    all_data = []
    files_to_analyze = ["data_2025-01-18_03-46-49.csv", "2025-01-04_19-37-52.csv"]
    
    for filename in files_to_analyze:
        try:
            data = load_csv_data(filename)
            all_data.extend(data)
            print(f"Loaded {len(data)} double_s1s2 samples from {filename}")
        except FileNotFoundError:
            print(f"Warning: Could not find {filename}")
    
    if not all_data:
        print("Error: No data files found!")
        return
    
    print(f"Total samples for analysis: {len(all_data)}")
    
    # Perform detailed failure analyses
    analyze_parameter_by_failure_type(all_data)
    analyze_height_failure_patterns(all_data)
    analyze_crack_failure_patterns(all_data)
    identify_robust_parameter_windows(all_data)
    
    # Final recommendations
    print("\n" + "="*70)
    print("DETAILED RECOMMENDATIONS")
    print("="*70)
    
    total_samples = len(all_data)
    success_count = sum(1 for row in all_data if row.get('weld_failure_type') == 1)
    height_fail_count = sum(1 for row in all_data if row.get('weld_failure_type') == 4)
    crack_fail_count = sum(1 for row in all_data if row.get('weld_failure_type') == 8)
    
    print(f"\nCurrent Status:")
    print(f"- Success Rate: {(success_count/total_samples)*100:.1f}% ({success_count}/{total_samples})")
    print(f"- Height Failures: {(height_fail_count/total_samples)*100:.1f}% ({height_fail_count}/{total_samples})")
    print(f"- Crack Failures: {(crack_fail_count/total_samples)*100:.1f}% ({crack_fail_count}/{total_samples})")
    
    print(f"\nImmediate Actions:")
    print(f"1. Investigate height failures - appear to have excessive weld penetration")
    print(f"2. Optimize S1 current and hold time based on success rate analysis")
    print(f"3. Consider process control limits to prevent parameter drift")
    print(f"4. Design confirmation experiments for identified robust windows")

if __name__ == "__main__":
    main()
